import { Rule } from "sanity";

export const validatedArrayStringOrTextLength = (
  rule: Rule,
  { maxLength, fieldName }: { maxLength: number; fieldName: string },
) => {
  return rule.custom((value) => {
    if (!value || !Array.isArray(value)) {
      return `${fieldName} é obrigatório`;
    }

    for (const item of value) {
      if (item && typeof item === "object" && "value" in item) {
        const textValue = item.value;
        if (typeof textValue === "string" && textValue.length > maxLength) {
          return `${fieldName} não pode exceder ${maxLength} caracteres`;
        }
      }
    }

    return true;
  });
};
