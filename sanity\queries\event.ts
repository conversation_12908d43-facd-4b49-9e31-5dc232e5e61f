import { EventFiltersType } from "@/app/(site)/event/schema";
import { defineQuery } from "next-sanity";

const fullEventFields = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  "name": coalesce(
    name[_key == $lang][0].value,
    name[_key == "pt"][0].value,
    "Translation missing"
  ),
  organizer,
  "description": coalesce(
    description[_key == $lang][0].value,
    description[_key == "pt"][0].value,
    "Translation missing"
  ),
  location,
  startAt,
  endAt,
  highlightedUntil,
  sponsoredUntil,
  "slug": slug.current,
  prices[] {
    name,
    price,
    description
  },
  categories[]-> {
    _id,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
  },
  checkoutMethods[] {
  name,
  type,
  value
},
  medias[] {
    _type,
    asset->{
      _type,
      url
    },
    alt,
    hotspot
  },
  createdBy
`;

const eventFields = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  "name": coalesce(
    name[_key == $lang][0].value,
    name[_key == "pt"][0].value,
    "Translation missing"
  ),
  organizer,
  "description": coalesce(
    description[_key == $lang][0].value,
    description[_key == "pt"][0].value,
    "Translation missing"
  ),
  location,
  startAt,
  endAt,
  highlightedUntil,
  sponsoredUntil,
  "slug": slug.current,
  prices[] {
    name,
    price,
    description
  },
  categories[]-> {
    _id,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
  },
  medias[] {
    _type,
    asset->{
      _type,
      url
    },
    alt,
    hotspot
  },
  createdBy
`;

export const getAllEventsQuery = (filters?: EventFiltersType) => {
  const conditions = [
    '_type == "event" && defined(slug.current) && dateTime(startAt) >= dateTime(now())',
    filters?.highlightedUntil &&
      "dateTime(highlightedUntil) >= dateTime(now())",
    filters?.sponsoredUntil && "dateTime(sponsoredUntil) >= dateTime(now())",
    filters?.searchKey &&
      `coalesce(name[_key == $lang][0].value, name[_key == "pt"][0].value) match "${filters.searchKey}*"`,
    filters?.categoryId && `"${filters.categoryId}" in categories[]->_id`,
    filters?.organizer && `organizer match "${filters.organizer}"`,
    // filters?.startAt && `dateTime(startAt) >= dateTime("${filters.startAt}")`,
    filters?.location && `location match "${filters.location}"`,
  ]
    .filter(Boolean)
    .join(" && ");

  const orderBy = (_sortBy: EventFiltersType["sortBy"]) => {
    if (!_sortBy) return "";
    switch (_sortBy) {
      case "name":
        return `| order(coalesce(name[_key == $lang][0].value, name[_key == "pt"][0].value) ${filters?.sortOrder || "asc"})`;
      case "description":
        return `| order(coalesce(description[_key == $lang][0].value, description[_key == "pt"][0].value) ${filters?.sortOrder || "asc"})`;
      case "prices":
        return `| order(prices[0].price ${filters?.sortOrder || "asc"})`;
      case "highlightedUntil":
        return `| order(highlightedUntil ${filters?.sortOrder || "asc"})`;
      case "sponsoredUntil":
        return `| order(sponsoredUntil ${filters?.sortOrder || "asc"})`;
      default:
        return `| order(${_sortBy} ${filters?.sortOrder || "asc"})`;
    }
  };

  const limitClause = filters?.limit ? `[0...${filters.limit}]` : "";

  return defineQuery(
    `*[${conditions}] ${orderBy(filters?.sortBy)} {${eventFields}} ${limitClause}`,
  );
};

export const getEventBySlugQuery = defineQuery(`
  *[_type == "event" && slug.current == $slug][0] {
    ${fullEventFields}
  }
`);

export const getEventByIdQuery = defineQuery(`
  *[_type == "event" && _id == $id][0] {
    ${fullEventFields}
  }
`);

export const getEventsByIdsQuery = defineQuery(`
  *[_type == "event" && _id in $ids] {
    ${eventFields}
  }
`);

export const getEventLocationsQuery = defineQuery(`
  *[_type == "event" && defined(location)] {
    "location": location
  } | order(location asc) | unique()
`);

export const getEventOrganizersQuery = defineQuery(`
  *[_type == "event" && defined(organizer)] {
    "name": organizer
  } | order(organizer asc) | unique()
`);

// Favorite queries
export const getUserFavoriteEventsQuery = defineQuery(`
  *[_type == "favoriteEvent" && userId == $userId] {
    "event": event->{
      ${eventFields}
    }
  }.event
`);

export const hasUserFavoritedQuery = defineQuery(`
  *[_type == "favoriteEvent" && userId == $userId && eventId == $eventId][0] {
    _id
  }
`);
