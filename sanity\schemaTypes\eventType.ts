import { EVENT_CHECKOUT_METHODS_TYPES } from "@/app/(site)/event/schema";
import { Calendar } from "lucide-react";
import { defineField, defineType } from "sanity";
import { validatedArrayStringOrTextLength } from "./utils";

export const eventType = defineType({
  name: "event",
  title: "Eventos",
  type: "document",
  icon: Calendar,
  fields: [
    defineField({
      name: "name",
      title: "Nome",
      type: "internationalizedArrayString",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          maxLength: 100,
          fieldName: "Nome do evento",
        }),
    }),
    defineField({
      name: "slug",
      type: "slug",
      options: {
        source: "name",
      },
    }),
    defineField({
      name: "organizer",
      title: "Organizador",
      type: "string",
      validation: (rule) =>
        rule
          .max(50)
          .warning("Nome do organizador deve ter no máximo 50 caracteres")
          .required()
          .error("Organizador é obrigatório"),
    }),
    defineField({
      name: "description",
      title: "Descri<PERSON>",
      type: "internationalizedArrayText",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          maxLength: 1000,
          fieldName: "Descrição",
        }),
    }),
    defineField({
      name: "location",
      title: "Local",
      type: "string",
      validation: (rule) =>
        rule
          .max(100)
          .warning("Local deve ter no máximo 100 caracteres")
          .required()
          .error("Local é obrigatório"),
    }),
    defineField({
      name: "startAt",
      title: "Data de Início",
      type: "datetime",
      validation: (rule) =>
        rule.required().error("Data de início é obrigatória"),
    }),
    defineField({
      name: "endAt",
      title: "Data de Fim",
      type: "datetime",
      validation: (rule) => rule.required().error("Data de fim é obrigatória"),
    }),
    defineField({
      name: "highlightedUntil",
      title: "Em destaque até",
      type: "datetime",
    }),
    defineField({
      name: "sponsoredUntil",
      title: "Patrocinado até",
      type: "datetime",
    }),
    defineField({
      name: "prices",
      title: "Preços",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              type: "string",
              validation: (rule) =>
                rule
                  .max(50)
                  .warning("Nome deve ter no máximo 50 caracteres")
                  .required()
                  .error("Nome é obrigatório"),
            },
            {
              name: "price",
              type: "number",
              validation: (rule) =>
                rule.required().error("Preço é obrigatório"),
            },
            {
              name: "description",
              type: "text",
              validation: (rule) =>
                rule
                  .max(1000)
                  .warning("Descrição deve ter no máximo 1000 caracteres"),
            },
          ],
        },
      ],
      validation: (rule) =>
        rule
          .min(1)
          .warning("Ao menos um preço deve ser definido")
          .required()
          .error("Ao menos um preço deve ser definido"),
    }),
    defineField({
      name: "categories",
      title: "Categorias",
      type: "array",
      of: [{ type: "reference", to: { type: "category" } }],
    }),
    defineField({
      name: "medias",
      title: "Imagens",
      type: "array",
      of: [
        {
          type: "image",
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: "alt",
              type: "string",
              validation: (rule) =>
                rule
                  .max(200)
                  .warning("Descrição deve ter no máximo 200 caracteres"),
            },
          ],
        },
      ],
    }),
    defineField({
      name: "checkoutMethods",
      title: "Métodos de Checkout",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Nome",
              type: "string",
              validation: (rule) => [
                rule.required().error("Nome é obrigatório"),
                // rule.unique().error("Nome deve ser único"),
              ],
            },
            {
              name: "type",
              title: "Tipo",
              type: "string",
              options: {
                list: Object.entries(EVENT_CHECKOUT_METHODS_TYPES).map(
                  ([key, value]) => ({ title: value, value: key }),
                ),
                layout: "dropdown",
              },
              validation: (rule) => rule.required().error("Tipo é obrigatório"),
            },
            {
              name: "value",
              title: "Valor",
              type: "string",
              validation: (rule) =>
                rule.required().error("Valor é obrigatório"),
            },
          ],
        },
      ],
    }),
    defineField({
      name: "createdBy",
      title: "Criado por",
      type: "string",
      validation: (rule) =>
        rule
          .max(50)
          .warning("Nome do criador deve ter no máximo 50 caracteres")
          .required()
          .error("Nome do criador é obrigatório"),
    }),
  ],
  preview: {
    select: {
      title: "name.[0].value",
      subtitle: "organizer",
      media: "medias.0",
    },
  },
});

